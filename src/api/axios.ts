import axios from "axios";
import tokenManager from "../utils/tokenManager";

const API_BASE_URL = "http://localhost:8002"; // Assuming backend runs on port 8002

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

api.interceptors.request.use(
  (config) => {
    // Usar Token Manager para obter token válido
    const token = tokenManager.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor de resposta para lidar com tokens expirados
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Se receber 401, limpar token e redirecionar para login
    if (error.response?.status === 401) {
      console.log("🔒 Token inválido ou expirado, limpando sessão");
      tokenManager.clearAll();

      // Redirecionar para login se não estiver já na página de login
      if (window.location.pathname !== "/login") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export default api;
