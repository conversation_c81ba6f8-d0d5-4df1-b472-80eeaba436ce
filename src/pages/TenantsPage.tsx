import React, { useState, useEffect } from 'react';
import api from '../api/axios';
import axios from 'axios';
import { useNotification } from '../context/NotificationContext';

interface TenantAdminResponse {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  has_api_key: boolean;
}

const TenantsPage: React.FC = () => {
  const [tenants, setTenants] = useState<TenantAdminResponse[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null); // Keep local error for initial fetch
  const [showApiKeyModal, setShowApiKeyModal] = useState<boolean>(false);
  const [selectedTenant, setSelectedTenant] = useState<TenantAdminResponse | null>(null);
  const [newApiKey, setNewApiKey] = useState<string>('');
  const { addNotification } = useNotification();

  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await api.get<TenantAdminResponse[]>('/api/v1/admin/tenants');
      setTenants(response.data);
    } catch (err) {
      if (api.isAxiosError(err) && err.response) {
        setError(err.response.data.detail || 'Failed to fetch tenants');
      } else {
        setError('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTenants();
  }, []);

  const handleManageApiKey = (tenant: TenantAdminResponse) => {
    setSelectedTenant(tenant);
    setNewApiKey(''); // Clear previous API key input
    setShowApiKeyModal(true);
  };

  const handleCloseModal = () => {
    setShowApiKeyModal(false);
    setSelectedTenant(null);
    setNewApiKey('');
  };

  const handleSubmitApiKey = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTenant || !newApiKey) return;

    try {
      const url = `/api/v1/admin/tenants/${selectedTenant.id}/api-key`;
      const data = { tenant_id: selectedTenant.id, api_key: newApiKey };

      if (selectedTenant.has_api_key) {
        await api.put(url, data);
        addNotification(`API Key for ${selectedTenant.name} updated successfully!`, 'success');
      } else {
        await api.post(url, data);
        addNotification(`API Key for ${selectedTenant.name} created successfully!`, 'success');
      }

      handleCloseModal();
      fetchTenants(); // Refetch tenants to update API key status
    } catch (err) {
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data.detail || 'Failed to fetch tenants');
        addNotification(err.response.data.detail || 'Failed to fetch tenants', 'error');
      } else {
        setError('An unexpected error occurred');
        addNotification('An unexpected error occurred', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRevokeApiKey = async (tenant: TenantAdminResponse) => {
    if (window.confirm(`Are you sure you want to revoke the API Key for ${tenant.name}?`)) {
      try {
        await api.delete(`/api/v1/admin/tenants/${tenant.id}/api-key`);
        addNotification(`API Key for ${tenant.name} revoked successfully!`, 'success');
        fetchTenants(); // Refetch tenants to update API key status
      } catch (err) {
        if (api.isAxiosError(err) && err.response) {
          addNotification(err.response.data.detail || 'Failed to revoke API key', 'error');
        } else {
          addNotification('An unexpected error occurred', 'error');
        }
      }
    }
  };

  if (loading) {
    return <div>Loading tenants...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Tenant Management</h2>
      <table className="min-w-full bg-white border border-gray-300">
        <thead>
          <tr className="bg-gray-100">
            <th className="py-2 px-4 border-b">ID</th>
            <th className="py-2 px-4 border-b">Name</th>
            <th className="py-2 px-4 border-b">Description</th>
            <th className="py-2 px-4 border-b">Is Active</th>
            <th className="py-2 px-4 border-b">Has API Key</th>
            <th className="py-2 px-4 border-b">Actions</th>
          </tr>
        </thead>
        <tbody>
          {tenants.map((tenant) => (
            <tr key={tenant.id} className="hover:bg-gray-50">
              <td className="py-2 px-4 border-b">{tenant.id}</td>
              <td className="py-2 px-4 border-b">{tenant.name}</td>
              <td className="py-2 px-4 border-b">{tenant.description || 'N/A'}</td>
              <td className="py-2 px-4 border-b">{tenant.is_active ? 'Yes' : 'No'}</td>
              <td className="py-2 px-4 border-b">{tenant.has_api_key ? 'Yes' : 'No'}</td>
              <td className="py-2 px-4 border-b">
                <button onClick={() => handleManageApiKey(tenant)} className="bg-blue-500 text-white px-3 py-1 rounded mr-2 hover:bg-blue-600">Manage API Key</button>
                {tenant.has_api_key && (
                  <button onClick={() => handleRevokeApiKey(tenant)} className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600">Revoke API Key</button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {showApiKeyModal && selectedTenant && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-center">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h3 className="text-xl font-bold mb-4">{selectedTenant.name} - API Key Management</h3>
            <form onSubmit={handleSubmitApiKey}>
              <div className="mb-4">
                <label htmlFor="newApiKey" className="block text-gray-700 text-sm font-bold mb-2">New API Key:</label>
                <input
                  type="text"
                  id="newApiKey"
                  value={newApiKey}
                  onChange={(e) => setNewApiKey(e.target.value)}
                  required
                  minLength={32}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button type="button" onClick={handleCloseModal} className="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">Cancel</button>
                <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Save API Key</button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default TenantsPage;
