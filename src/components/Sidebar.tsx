import React from 'react';
import { Link, useNavigate } from 'react-router-dom';

const Sidebar: React.FC = () => {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('admin_token'); // Assumindo que o token é armazenado aqui
    // Se houver um endpoint de logout no backend, você faria uma requisição aqui
    navigate('/login');
  };

  return (
    <aside className="w-52 bg-gray-100 p-4 h-screen-minus-header">
      <nav>
        <ul>
          <li className="mb-4"><Link to="/">Home</Link></li>
          <li className="mb-4"><Link to="/dashboard">Dashboard</Link></li>
          <li className="mb-4"><Link to="/tenants">Tenants</Link></li>
          <li className="mb-4"><Link to="/login">Login</Link></li>
          <li className="mb-4">
            <button onClick={handleLogout} className="text-blue-600 hover:underline">
              Logout
            </button>
          </li>
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
