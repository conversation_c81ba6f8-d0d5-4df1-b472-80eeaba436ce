/**
 * Token Manager - Gerenciamento seguro de tokens de autenticação
 * 
 * Migração de localStorage para sessionStorage para melhor segurança
 * Adiciona validação de expiração e limpeza automática
 * Preparado para migração futura para HTTP-only cookies
 */

interface TokenData {
  token: string;
  expiresAt: number; // timestamp
  issuedAt: number;  // timestamp
}

class TokenManager {
  private readonly TOKEN_KEY = 'admin_token_data';
  private readonly STORAGE_TYPE = 'session'; // 'session' | 'local' | 'cookie'
  
  /**
   * Armazena o token com metadados de expiração
   */
  setToken(token: string, expiresInMinutes: number = 30): void {
    try {
      const now = Date.now();
      const tokenData: TokenData = {
        token,
        issuedAt: now,
        expiresAt: now + (expiresInMinutes * 60 * 1000)
      };

      if (this.STORAGE_TYPE === 'session') {
        sessionStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokenData));
      } else {
        // Fallback para localStorage se necessário
        localStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokenData));
      }

      // Log para auditoria (remover em produção)
      console.log('🔐 Token armazenado com segurança', {
        storage: this.STORAGE_TYPE,
        expiresIn: `${expiresInMinutes} minutos`,
        expiresAt: new Date(tokenData.expiresAt).toISOString()
      });

    } catch (error) {
      console.error('❌ Erro ao armazenar token:', error);
      throw new Error('Falha ao armazenar token de autenticação');
    }
  }

  /**
   * Recupera o token se válido, null se expirado ou inexistente
   */
  getToken(): string | null {
    try {
      const storage = this.STORAGE_TYPE === 'session' ? sessionStorage : localStorage;
      const tokenDataStr = storage.getItem(this.TOKEN_KEY);
      
      if (!tokenDataStr) {
        return null;
      }

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      const now = Date.now();

      // Verifica se o token expirou
      if (now >= tokenData.expiresAt) {
        console.log('⏰ Token expirado, removendo automaticamente');
        this.removeToken();
        return null;
      }

      // Verifica se o token está próximo do vencimento (5 minutos)
      const timeToExpiry = tokenData.expiresAt - now;
      if (timeToExpiry < 5 * 60 * 1000) {
        console.warn('⚠️ Token expira em menos de 5 minutos');
      }

      return tokenData.token;

    } catch (error) {
      console.error('❌ Erro ao recuperar token:', error);
      // Se houver erro na estrutura, limpa o storage
      this.removeToken();
      return null;
    }
  }

  /**
   * Verifica se existe um token válido
   */
  hasValidToken(): boolean {
    return this.getToken() !== null;
  }

  /**
   * Remove o token do storage
   */
  removeToken(): void {
    try {
      if (this.STORAGE_TYPE === 'session') {
        sessionStorage.removeItem(this.TOKEN_KEY);
      } else {
        localStorage.removeItem(this.TOKEN_KEY);
      }
      
      console.log('🗑️ Token removido com sucesso');
    } catch (error) {
      console.error('❌ Erro ao remover token:', error);
    }
  }

  /**
   * Obtém informações sobre o token atual
   */
  getTokenInfo(): { isValid: boolean; expiresAt?: Date; timeToExpiry?: number } | null {
    try {
      const storage = this.STORAGE_TYPE === 'session' ? sessionStorage : localStorage;
      const tokenDataStr = storage.getItem(this.TOKEN_KEY);
      
      if (!tokenDataStr) {
        return null;
      }

      const tokenData: TokenData = JSON.parse(tokenDataStr);
      const now = Date.now();
      const isValid = now < tokenData.expiresAt;

      return {
        isValid,
        expiresAt: new Date(tokenData.expiresAt),
        timeToExpiry: tokenData.expiresAt - now
      };

    } catch (error) {
      console.error('❌ Erro ao obter informações do token:', error);
      return null;
    }
  }

  /**
   * Limpa todos os dados de autenticação (logout completo)
   */
  clearAll(): void {
    this.removeToken();
    
    // Limpa também localStorage como precaução (migração)
    try {
      localStorage.removeItem('admin_token');
      localStorage.removeItem(this.TOKEN_KEY);
    } catch (error) {
      console.warn('⚠️ Erro ao limpar localStorage:', error);
    }
  }

  /**
   * Migra token existente do localStorage para o novo sistema
   */
  migrateFromLocalStorage(): boolean {
    try {
      const oldToken = localStorage.getItem('admin_token');
      
      if (oldToken && !this.hasValidToken()) {
        console.log('🔄 Migrando token do localStorage...');
        
        // Assume 30 minutos de validade para token migrado
        this.setToken(oldToken, 30);
        
        // Remove o token antigo
        localStorage.removeItem('admin_token');
        
        console.log('✅ Migração concluída com sucesso');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ Erro na migração:', error);
      return false;
    }
  }
}

// Instância singleton
export const tokenManager = new TokenManager();

// Executa migração automática na inicialização
tokenManager.migrateFromLocalStorage();

export default tokenManager;
