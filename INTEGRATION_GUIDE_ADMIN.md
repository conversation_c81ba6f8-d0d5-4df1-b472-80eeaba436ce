### Guia de Integração do Frontend com o Serviço de Autenticação (Rotas Admin)

Este guia detalha como o frontend deve interagir com o serviço de autenticação, com foco nas rotas exclusivas de administração para gerenciamento de API Keys.

#### 1. Introdução

O serviço de autenticação é responsável por gerenciar usuários, autenticação e API Keys para as diversas aplicações cliente. As rotas `/api/v1/admin/*` são protegidas e destinadas exclusivamente ao frontend de administração para operações de CRUD de API Keys de tenants.

#### 2. URL Base da API

A URL base para todas as requisições à API é:
`http://localhost:8002` (para desenvolvimento local)
`https://quanthea-auth-service.onrender.com` (para deploy em produção, ex: `https://auth.suaempresa.com`)

#### 3. Fluxo de Autenticação (Admin)

Para acessar as rotas de administração, o frontend deve primeiro autenticar um usuário administrador.

**Endpoint de Login:**

- **Endpoint:** `POST /api/v1/admin/login`
- **Descrição:** Autentica um usuário administrador com email e senha.
- **Request Body (JSON):**
  ```json
  {
    "username": "<EMAIL>",
    "password": "sua_senha_secreta"
  }
  ```
  _Nota: O campo `username` espera o email do administrador._
- **Headers:** `Content-Type: application/x-www-form-urlencoded` (o FastAPI espera `OAuth2PasswordRequestForm` que é `application/x-www-form-urlencoded` por padrão, mas pode ser enviado como JSON se o frontend usar `FormData` ou similar).
- **Response (JSON - Sucesso):**
  ```json
  {
    "access_token": "eyJhbGciOiJIUzI1Ni...",
    "token_type": "bearer"
  }
  ```
- **Response (JSON - Erro):**
  ```json
  {
    "detail": "Incorrect email or password"
  }
  ```

**Uso do Token JWT:**

Após o login bem-sucedido, o frontend receberá um `access_token`. Este token deve ser armazenado de forma segura (ex: `localStorage` ou `sessionStorage`) e incluído em todas as requisições subsequentes às rotas protegidas de administração no cabeçalho `Authorization`.

**Exemplo de Requisição com Token:**

```javascript
// Exemplo em JavaScript (usando fetch API)
const token = "eyJhbGciOiJIUzI1Ni..."; // Token obtido do login

fetch("http://localhost:8002/api/v1/admin/tenants", {
  method: "GET",
  headers: {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  },
})
  .then((response) => response.json())
  .then((data) => console.log(data))
  .catch((error) => console.error("Erro:", error));
```

#### 4. Considerações sobre CORS

O serviço de autenticação implementa uma política de CORS dinâmica:

- **Desenvolvimento:** Todas as origens (`*`) são permitidas para facilitar o desenvolvimento.
- **Produção:** Apenas as origens configuradas nas variáveis de ambiente `CORS_ORIGINS` (para aplicações cliente) e `ADMIN_FRONTEND_URL` (para o frontend de administração) serão permitidas.

**Atenção:** Certifique-se de que a URL do seu frontend de administração (`http://localhost:5173` em desenvolvimento, e a URL de produção no deploy) esteja configurada corretamente na variável de ambiente `ADMIN_FRONTEND_URL` no ambiente de deploy do backend.

#### 5. Endpoints de Administração (CRUD de API Keys)

Todos os endpoints abaixo requerem autenticação com o token JWT do administrador no cabeçalho `Authorization: Bearer <token>`.

---

**5.1. Listar Tenants**

- **Endpoint:** `GET /api/v1/admin/tenants`
- **Descrição:** Lista todos os tenants registrados, indicando se possuem uma API Key associada (mas sem expor a chave em si).
- **Headers:** `Authorization: Bearer <token>`
- **Response (JSON):**
  ```json
  [
    {
      "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "name": "Minha Aplicação Web",
      "description": "Frontend principal da empresa",
      "is_active": true,
      "created_at": "2023-10-26T10:00:00.000Z",
      "updated_at": "2023-10-26T10:00:00.000Z",
      "has_api_key": true
    },
    {
      "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
      "name": "Aplicativo Mobile",
      "description": "App iOS e Android",
      "is_active": true,
      "created_at": "2023-10-27T11:00:00.000Z",
      "updated_at": "2023-10-27T11:00:00.000Z",
      "has_api_key": false
    }
  ]
  ```

---

**5.2. Criar/Atualizar API Key para um Tenant**

- **Endpoint:** `POST /api/v1/admin/tenants/{tenant_id}/api-key`
- **Descrição:** Gera e associa um **novo hash** de API Key a um tenant. Se o tenant já tiver uma API Key, esta será substituída.
- **Parâmetros de Path:** `tenant_id` (UUID do tenant)
- **Request Body (JSON):**
  ```json
  {
    "api_key": "sua_nova_api_key_secreta_aqui"
  }
  ```
- **Headers:** `Authorization: Bearer <token>`, `Content-Type: application/json`
- **Response (JSON):**
  ```json
  {
    "message": "API Key for tenant a1b2c3d4-e5f6-7890-1234-567890abcdef created/updated successfully"
  }
  ```

---

**5.3. Atualizar API Key para um Tenant (PUT)**

- **Endpoint:** `PUT /api/v1/admin/tenants/{tenant_id}/api-key`
- **Descrição:** Atualiza o hash da API Key para um tenant existente. Funcionalmente similar ao `POST` acima para criação/substituição.
- **Parâmetros de Path:** `tenant_id` (UUID do tenant)
- **Request Body (JSON):**
  ```json
  {
    "api_key": "outra_api_key_secreta_aqui"
  }
  ```
- **Headers:** `Authorization: Bearer <token>`, `Content-Type: application/json`
- **Response (JSON):**
  ```json
  {
    "message": "API Key for tenant a1b2c3d4-e5f6-7890-1234-567890abcdef updated successfully"
  }
  ```

---

**5.4. Remover API Key de um Tenant**

- **Endpoint:** `DELETE /api/v1/admin/tenants/{tenant_id}/api-key`
- **Descrição:** Remove o hash da API Key de um tenant, revogando efetivamente seu acesso à API.
- **Parâmetros de Path:** `tenant_id` (UUID do tenant)
- **Headers:** `Authorization: Bearer <token>`
- **Response (JSON):**
  ```json
  {
    "message": "API Key for tenant a1b2c3d4-e5f6-7890-1234-567890abcdef revoked successfully"
  }
  ```

#### 6. Tratamento de Erros Comuns

O serviço pode retornar os seguintes códigos de status HTTP:

- **`400 Bad Request`**: Requisição malformada, dados inválidos (ex: email ou senha incorretos no login).
- **`401 Unauthorized`**: Nenhuma credencial de autenticação fornecida ou credenciais inválidas (ex: token JWT ausente ou expirado).
- **`403 Forbidden`**: Credenciais válidas, mas o usuário não tem permissão para acessar o recurso (ex: token JWT de usuário comum tentando acessar rota admin).
- **`404 Not Found`**: Recurso não encontrado (ex: `tenant_id` inexistente).
- **`500 Internal Server Error`**: Erro inesperado no servidor.

Sempre verifique o campo `detail` na resposta JSON para mais informações sobre o erro.

#### 7. Logout

Não há um endpoint de "logout" no servidor. Para "deslogar" um usuário administrador, o frontend deve simplesmente **remover o token JWT armazenado** (ex: `localStorage.removeItem('admin_token')`). O token se tornará inválido automaticamente após sua expiração.

#### 8. Configuração Essencial para o Frontend

Ao fazer o deploy do frontend, certifique-se de que a URL base da API esteja configurada corretamente para o ambiente de produção.

---
